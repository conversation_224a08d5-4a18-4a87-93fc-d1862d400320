<?php
echo "PHP يعمل بشكل صحيح!<br>";
echo "التاريخ والوقت: " . date('Y-m-d H:i:s') . "<br>";
echo "إصدار PHP: " . phpversion() . "<br>";

// اختبار الاتصال بقاعدة البيانات
try {
    $conn = new mysqli('localhost', 'root', '', 'school_management');
    if ($conn->connect_error) {
        echo "خطأ في الاتصال بقاعدة البيانات: " . $conn->connect_error;
    } else {
        echo "الاتصال بقاعدة البيانات ناجح!<br>";
        
        // اختبار وجود جدول users
        $result = $conn->query("SHOW TABLES LIKE 'users'");
        if ($result->num_rows > 0) {
            echo "جدول users موجود!<br>";
            
            // عدد المستخدمين
            $count_result = $conn->query("SELECT COUNT(*) as count FROM users");
            $count = $count_result->fetch_assoc()['count'];
            echo "عدد المستخدمين: " . $count . "<br>";
        } else {
            echo "جدول users غير موجود!<br>";
        }
    }
    $conn->close();
} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
